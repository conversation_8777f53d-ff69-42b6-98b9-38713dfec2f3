{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 473, "versionNonce": 1256506895, "isDeleted": false, "id": "ox1Blt2bzl0onmQfB7ZAN", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 827.69921875, "y": 210.68359375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 251.7109375, "height": 259.47265625, "seed": 1508024704, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [{"id": "sE5xq9Fz5VDTWcJGhJizg", "type": "arrow"}, {"id": "HZoAI_wR8CyRR9fVaFwSl", "type": "arrow"}], "updated": 1660298248381, "link": null, "locked": false}, {"type": "text", "version": 585, "versionNonce": 720617423, "isDeleted": false, "id": "rjHz2X8U0ZDEyckj_tTSw", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 854.0546875, "y": 287.16015625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 199, "height": 160, "seed": 2100367744, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [{"id": "vJNRjoY0dZcqOBw5RzuHn", "type": "arrow"}], "updated": 1660298281789, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Reads temperatures\nfrom various\nModbus sensors (servers)\n\nS1: read index 0 -> 28\nS2: read index 0 -> 26\n...\n", "baseline": 154, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Reads temperatures\nfrom various\nModbus sensors (servers)\n\nS1: read index 0 -> 28\nS2: read index 0 -> 26\n...\n"}, {"type": "rectangle", "version": 534, "versionNonce": 2127380463, "isDeleted": false, "id": "mDgu1gSg34HbU-NAHPB30", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 420.2109375, "y": 212.580078125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 216.16406250000006, "height": 172.06640624999997, "seed": 1336837760, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [{"id": "sE5xq9Fz5VDTWcJGhJizg", "type": "arrow"}], "updated": 1660298020282, "link": null, "locked": false}, {"type": "text", "version": 653, "versionNonce": 2127484513, "isDeleted": false, "id": "UxvTnld8qh188IzV4uJ2q", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 443.79296875, "y": 286.3515625, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 178, "height": 80, "seed": 759374208, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660298185311, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Measures temperature\nand hygrometry:\n0: 28°C\n1: 32%  ", "baseline": 74, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Measures temperature\nand hygrometry:\n0: 28°C\n1: 32%  "}, {"type": "text", "version": 349, "versionNonce": 1469666511, "isDeleted": false, "id": "F6UUk6_B6uALmjxcHLYw4", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 488.19921875, "y": 237.921875, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 79, "height": 25, "seed": 354006656, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660298032950, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Sensor 1", "baseline": 18, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sensor 1"}, {"type": "text", "version": 224, "versionNonce": 864770191, "isDeleted": false, "id": "v7q2uvVFHZBvjr-y_ZJKl", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 884.5546875, "y": 238.328125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 150, "height": 25, "seed": 2108436864, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660299132232, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "libmodbus client", "baseline": 18, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "libmodbus client"}, {"type": "arrow", "version": 925, "versionNonce": 27208751, "isDeleted": false, "id": "sE5xq9Fz5VDTWcJGhJizg", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 656.03125, "y": 313.29799967005374, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 154.05078125, "height": 8.294130761394001, "seed": 455209344, "groupIds": [], "strokeSharpness": "round", "boundElements": [], "updated": 1660298248381, "link": null, "locked": false, "startBinding": {"elementId": "mDgu1gSg34HbU-NAHPB30", "focus": 0.08648410639065775, "gap": 19.65625}, "endBinding": {"elementId": "ox1Blt2bzl0onmQfB7ZAN", "focus": 0.08133464876815498, "gap": 17.6171875}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [154.05078125, 8.294130761394001]]}, {"type": "text", "version": 709, "versionNonce": 650449167, "isDeleted": false, "id": "Q6P32mRyop5JlKGPB3tei", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 5.552321937284518, "x": 679.345703125, "y": 433.1444738051471, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 79, "height": 15, "seed": 1091054019, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660298261423, "link": null, "locked": false, "fontSize": 11.542968749999993, "fontFamily": 1, "text": "TCP or serial", "baseline": 10, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "TCP or serial"}, {"type": "text", "version": 707, "versionNonce": 1999616833, "isDeleted": false, "id": "vETmEnYJoC4MKv7ysqjAv", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0.06779103263247777, "x": 683.3417968749999, "y": 281.21582031250006, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 79, "height": 15, "seed": 1043479501, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660298255264, "link": null, "locked": false, "fontSize": 11.542968749999993, "fontFamily": 1, "text": "TCP or serial", "baseline": 10, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "TCP or serial"}, {"type": "rectangle", "version": 587, "versionNonce": 85990017, "isDeleted": false, "id": "pHhCP3DIU5fE4v3yi3obG", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 426.513671875, "y": 424.2646484375, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 216.16406250000006, "height": 172.06640624999997, "seed": 1733016847, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [{"id": "sE5xq9Fz5VDTWcJGhJizg", "type": "arrow"}, {"id": "HZoAI_wR8CyRR9fVaFwSl", "type": "arrow"}], "updated": 1660298157321, "link": null, "locked": false}, {"type": "text", "version": 708, "versionNonce": 1824105825, "isDeleted": false, "id": "3oXICnKHoOzNPyb-PxNbt", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 450.095703125, "y": 498.0361328125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 178, "height": 80, "seed": 1378197345, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660298208836, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "Measures temperature\nand hygrometry:\n0: 26°C\n1: 40%  ", "baseline": 74, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Measures temperature\nand hygrometry:\n0: 26°C\n1: 40%  "}, {"type": "text", "version": 400, "versionNonce": 747588289, "isDeleted": false, "id": "rD_cbOE0Mwz-sfB0YqcaJ", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 494.501953125, "y": 449.6064453125, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 88, "height": 25, "seed": 68109103, "groupIds": [], "strokeSharpness": "sharp", "boundElements": [], "updated": 1660298172011, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "Sensor 2", "baseline": 18, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sensor 2"}, {"type": "arrow", "version": 1150, "versionNonce": 1550306895, "isDeleted": false, "id": "HZoAI_wR8CyRR9fVaFwSl", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 666.49609375, "y": 532.2655116636315, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 145.8515625, "height": 133.46442554799017, "seed": 1409289601, "groupIds": [], "strokeSharpness": "round", "boundElements": [], "updated": 1660298248381, "link": null, "locked": false, "startBinding": {"elementId": "pHhCP3DIU5fE4v3yi3obG", "focus": 0.7718436212944663, "gap": 23.818359375}, "endBinding": {"elementId": "ox1Blt2bzl0onmQfB7ZAN", "focus": 0.28922965891088237, "gap": 15.3515625}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [145.8515625, -133.46442554799017]]}, {"id": "feq5Rn2_gEHIfIzRPvNu6", "type": "rectangle", "x": 832.283203125, "y": 515.0556640625, "width": 255, "height": 76.1171875, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 826152751, "version": 65, "versionNonce": 19459567, "isDeleted": false, "boundElements": [{"type": "text", "id": "n4_rv3VhjyMXLSFrP52Vw"}, {"id": "vJNRjoY0dZcqOBw5RzuHn", "type": "arrow"}], "updated": 1660298281790, "link": null, "locked": false}, {"id": "n4_rv3VhjyMXLSFrP52Vw", "type": "text", "x": 837.283203125, "y": 540.6142578125, "width": 245, "height": 25, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 555916079, "version": 20, "versionNonce": 1538604143, "isDeleted": false, "boundElements": null, "updated": 1660298276528, "link": null, "locked": false, "text": "Database or logs", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "containerId": "feq5Rn2_gEHIfIzRPvNu6", "originalText": "Database or logs"}, {"id": "vJNRjoY0dZcqOBw5RzuHn", "type": "arrow", "x": 958.509765625, "y": 472.4150390625, "width": 0, "height": 39.33984375, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 1036508641, "version": 20, "versionNonce": 2130978977, "isDeleted": false, "boundElements": null, "updated": 1660298281790, "link": null, "locked": false, "points": [[0, 0], [0, 39.33984375]], "lastCommittedPoint": null, "startBinding": {"elementId": "rjHz2X8U0ZDEyckj_tTSw", "focus": -0.04979978015075378, "gap": 25.2548828125}, "endBinding": {"elementId": "feq5Rn2_gEHIfIzRPvNu6", "focus": -0.009987745098039217, "gap": 3.30078125}, "startArrowhead": null, "endArrowhead": "arrow"}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}