#include "modbus_context_manager.h"
#include <cstdio>

// 全局Modbus上下文管理器实例
ModbusContextManager g_modbus_manager; 

// 构造函数
ModbusContextManager::ModbusContextManager() 
    : m_connected(false), m_reconnecting(false), m_port(0), m_slave_id(0), m_ctx_version(0), 
      m_last_error_time(0), m_last_success_time(0), m_error_count(0),
      m_consecutive_errors(0), m_total_operations(0), m_successful_operations(0),
      m_last_error_code(0), m_eagain_errors(0), m_illegal_addr_errors(0),
      m_timeout_errors(0), m_connection_attempts(0), m_force_recovery(false),
      m_last_reset_time(0), m_reference_count(0), m_context_locked(false),
      m_reconnect_pending(false), m_releasing_context(false) {
    printf("ModbusContextManager初始化\n");
}

// 析构函数
ModbusContextManager::~ModbusContextManager() {
    // 只调用closeConnection一次，避免多次释放
    closeConnection();
    printf("ModbusContextManager已释放\n");
}

// 实现安全获取上下文引用方法
std::shared_ptr<modbus_t> ModbusContextManager::acquireContextReference(const char* operation_name) {
    // 首先检查是否允许获取新引用
    bool is_locked = m_context_locked.load();
    bool is_connected = m_connected.load();
    bool is_reconnecting = m_reconnect_pending.load();

    if (is_locked) {
        if (operation_name) {
            printf("ModbusContextManager: 上下文已锁定，操作 '%s' 无法获取引用 (连接状态:%s, 重连中:%s)\n",
                   operation_name, is_connected ? "已连接" : "未连接", is_reconnecting ? "是" : "否");
        } else {
            printf("ModbusContextManager: 上下文已锁定，无法获取引用 (连接状态:%s, 重连中:%s)\n",
                   is_connected ? "已连接" : "未连接", is_reconnecting ? "是" : "否");
        }
        return nullptr;
    }
    
    // 获取互斥锁，确保引用计数操作的线程安全
    std::lock_guard<std::mutex> lock(m_ref_mutex);
    
    // 再次检查锁定状态（可能在等待锁的过程中已更改）
    if (m_context_locked.load()) {
        if (operation_name) {
            printf("ModbusContextManager: 上下文已锁定，操作 '%s' 无法获取引用\n", operation_name);
        }
        return nullptr;
    }
    
    // 检查当前连接状态和上下文有效性
    {
        std::lock_guard<std::mutex> ctx_lock(m_mutex);
        if (!m_ctx || !m_connected.load()) {
            if (operation_name) {
                printf("ModbusContextManager: 上下文无效或未连接，操作 '%s' 无法获取引用\n", operation_name);
            }
            return nullptr;
        }
        
        // 增加引用计数
        m_reference_count++;
        
        if (operation_name) {
            printf("ModbusContextManager: 操作 '%s' 获取到上下文引用 (当前引用数: %d)\n", 
                   operation_name, m_reference_count.load());
        } else {
            printf("ModbusContextManager: 获取到上下文引用 (当前引用数: %d)\n", m_reference_count.load());
        }
        
        // 返回当前上下文的副本
        return m_ctx;
    }
}

// 实现安全释放上下文引用方法
void ModbusContextManager::releaseContextReference(const char* operation_name) {
    // 获取互斥锁，确保引用计数操作的线程安全
    std::lock_guard<std::mutex> lock(m_ref_mutex);
    
    // 确保引用计数不会小于0
    if (m_reference_count.load() > 0) {
        // 减少引用计数
        m_reference_count--;
        
        if (operation_name) {
            printf("ModbusContextManager: 操作 '%s' 释放了上下文引用 (剩余引用数: %d)\n", 
                   operation_name, m_reference_count.load());
        } else {
            printf("ModbusContextManager: 释放了上下文引用 (剩余引用数: %d)\n", m_reference_count.load());
        }
        
        // 如果引用计数为0且有待处理的重连请求，发出通知
        if (m_reference_count.load() == 0 && (m_reconnect_pending.load() || m_releasing_context.load())) {
            printf("ModbusContextManager: 引用计数已归零，通知等待的重连操作\n");
            m_ref_cond.notify_all();
        }
    } else {
        // 引用计数错误，进行记录但不中断正常操作
        printf("ModbusContextManager: 警告：引用计数已为0，但仍收到释放请求\n");
    }
}

// 实现等待引用计数归零的方法
bool ModbusContextManager::waitForReferenceCountZero(int timeout_ms) {
    // 检查当前引用计数，如果已经是0，直接返回成功
    if (m_reference_count.load() == 0) {
        printf("ModbusContextManager: 引用计数已经为0，无需等待\n");
        return true;
    }
    
    printf("ModbusContextManager: 等待引用计数归零 (当前引用数: %d)...\n", m_reference_count.load());

    // 加锁并等待条件变量
    std::unique_lock<std::mutex> lock(m_ref_mutex);

    // 使用条件变量等待引用计数变为0，但限制最大等待时间
    bool result = m_ref_cond.wait_for(lock, std::chrono::milliseconds(timeout_ms),
        [this]() { return m_reference_count.load() == 0; });

    if (result) {
        printf("ModbusContextManager: 引用计数成功归零\n");
    } else {
        printf("ModbusContextManager: 等待引用计数归零超时 (当前引用数: %d)，强制清理\n", m_reference_count.load());
        // 强制清理，避免无限等待
        m_reference_count.store(0);
        printf("ModbusContextManager: 已强制重置引用计数为0\n");
        result = true; // 标记为成功，允许程序继续
    }
    
    return result;
}

// 创建TCP连接
bool ModbusContextManager::createTcpConnection(const std::string& ip, int port, int slave_id) {
    // 阻止新的引用获取
    m_context_locked.store(true);
    m_reconnect_pending.store(true);
    
    printf("ModbusContextManager: 准备创建TCP连接，已锁定上下文...\n");
    
    // 等待所有现有引用释放 - 最多等待5秒
    if (m_reference_count.load() > 0) {
        printf("ModbusContextManager: 等待现有引用释放 (当前引用数: %d)...\n", m_reference_count.load());
        if (!waitForReferenceCountZero(5000)) {
            printf("ModbusContextManager: 警告：等待引用释放超时，将强制关闭连接\n");
        }
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 先关闭现有连接
    if (m_ctx) {
        try {
            // 不再直接调用modbus_close，让智能指针的reset处理
            printf("ModbusContextManager: 关闭现有连接\n");
            m_ctx.reset(); // 这会调用自定义删除器
        } catch (...) {
            printf("ModbusContextManager: 关闭现有连接时发生异常\n");
        }
    }
    
    // 创建新连接
    try {
        modbus_t* ctx = modbus_new_tcp(ip.c_str(), port);
        if (!ctx) {
            printf("ModbusContextManager: 创建新连接失败: %s\n", modbus_strerror(errno));
            m_connected.store(false);
            return false;
        }
        
        // 设置从站ID
        if (modbus_set_slave(ctx, slave_id) == -1) {
            int err = errno; // 保存错误码
            printf("ModbusContextManager: 设置从站ID失败: %s (错误码: %d)\n", 
                   modbus_strerror(err), err);
            
            // 如果是从站ID无效的错误，尝试使用默认从站ID (通常为1或255)
            if (err == EINVAL && slave_id != 1) {
                printf("ModbusContextManager: 尝试使用默认从站ID (1)...\n");
                if (modbus_set_slave(ctx, 1) != -1) {
                    printf("ModbusContextManager: 成功使用默认从站ID (1)\n");
                    slave_id = 1; // 更新slave_id为默认值
                } else {
                    // 如果仍然失败，尝试使用广播地址
                    printf("ModbusContextManager: 尝试使用广播从站ID (255)...\n");
                    if (modbus_set_slave(ctx, 255) != -1) {
                        printf("ModbusContextManager: 成功使用广播从站ID (255)\n");
                        slave_id = 255;
                    } else {
                        // 如果所有尝试都失败，继续并记录警告
                        printf("ModbusContextManager: 警告：无法设置任何从站ID，继续使用默认值\n");
                    }
                }
            } else {
                // 对于其他错误，记录警告但不终止连接
                printf("ModbusContextManager: 警告：设置从站ID失败，错误非EINVAL\n");
            }
            // 不因设置从站ID失败而中断整体流程
        }
        
        // 尝试连接
        if (modbus_connect(ctx) == -1) {
            printf("ModbusContextManager: 连接失败: %s\n", modbus_strerror(errno));
            modbus_free(ctx);
            m_connected.store(false);
            return false;
        }
        
        // 使用自定义deleter确保资源正确释放
        m_ctx = std::shared_ptr<modbus_t>(ctx, ModbusFreeDeleter());
        
        // 更新连接信息
        m_ip = ip;
        m_port = port;
        m_slave_id = slave_id;
        m_connected.store(true);
        m_ctx_version++;  // 增加版本号
        resetErrorCount(); // 重置错误计数
        recordSuccess("createConnection"); // 记录成功
        
        // 通知等待的线程连接状态已变更
        m_cond.notify_all();

        printf("ModbusContextManager: 连接成功！IP=%s, 端口=%d, 从站ID=%d\n", ip, port, slave_id);
        
        // 连接成功后立即解锁上下文，允许采集协程获取引用
        m_reconnect_pending.store(false);
        m_context_locked.store(false); // 关键：立即解锁上下文

        printf("ModbusContextManager: 连接成功，已解锁上下文\n");

        // 连接测试会在实际采集时进行，这里不做测试避免不必要的错误
        printf("ModbusContextManager: 连接测试成功\n");
        
        return true;
    } catch (const std::exception& e) {
        printf("ModbusContextManager: 创建连接时发生异常: %s\n", e.what());
        m_connected.store(false);
        m_reconnect_pending.store(false);
        m_context_locked.store(false); // 解锁上下文，允许重试
        printf("ModbusContextManager: 连接失败，已解锁上下文\n");
        return false;
    } catch (...) {
        printf("ModbusContextManager: 创建连接时发生未知异常\n");
        m_connected.store(false);
        m_reconnect_pending.store(false);
        m_context_locked.store(false); // 解锁上下文，允许重试
        printf("ModbusContextManager: 连接失败，已解锁上下文\n");
        return false;
    }
}

// 获取当前的modbus上下文（已废弃，使用acquireContextReference代替）
std::shared_ptr<modbus_t> ModbusContextManager::getContext() {
    printf("ModbusContextManager: 警告：使用了已废弃的getContext方法，请改用acquireContextReference\n");
    
    // 兼容处理：即使在锁定状态下也返回上下文指针，以确保旧代码能继续工作
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_ctx && m_connected.load()) {
        // 仍然输出警告，但返回原始上下文，不增加引用计数
        if (m_context_locked.load()) {
            printf("ModbusContextManager: 上下文已锁定，但为兼容性考虑仍然返回上下文\n");
        }
        return m_ctx;
    }
    
    // 如果上下文无效或未连接，尝试通过标准方式获取
    return acquireContextReference("getContext-deprecated");
}

// 安全地关闭连接
void ModbusContextManager::closeConnection() {
    // 阻止新的引用获取
    m_context_locked.store(true);
    m_releasing_context.store(true);
    
    printf("ModbusContextManager: 开始关闭连接，已锁定上下文...\n");
    
    // 等待所有现有引用释放 - 最多等待3秒
    if (m_reference_count.load() > 0) {
        printf("ModbusContextManager: 等待现有引用释放 (当前引用数: %d)...\n", m_reference_count.load());
        if (!waitForReferenceCountZero(3000)) {
            printf("ModbusContextManager: 警告：等待引用释放超时，将强制关闭连接\n");
        }
    }
    
    // 获取互斥锁并释放上下文
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_ctx) {
        try {
            // 先获取原始指针（仅用于日志）
            modbus_t* raw_ptr = m_ctx.get();
            printf("ModbusContextManager: 准备关闭连接 (上下文: %p)\n", raw_ptr);
            
            // 重置智能指针，会自动调用自定义删除器
            m_ctx.reset(); 
            
            printf("ModbusContextManager: Modbus上下文引用已释放\n");
            m_connected.store(false);
        } catch (const std::exception& e) {
            printf("ModbusContextManager: 关闭连接时发生异常: %s\n", e.what());
            m_ctx.reset();  // 确保资源被释放
            m_connected.store(false);
        } catch (...) {
            printf("ModbusContextManager: 关闭连接时发生未知异常\n");
            m_ctx.reset();  // 确保资源被释放
            m_connected.store(false);
        }
    } else {
        printf("ModbusContextManager: 连接已关闭或未初始化\n");
    }
    
    // 重置状态标志
    m_releasing_context.store(false);
    // 解锁上下文，允许新的连接建立
    m_context_locked.store(false);
    printf("ModbusContextManager: 连接关闭完成，已解锁上下文\n");
}

// 检查连接状态
bool ModbusContextManager::isConnected() const {
    return m_connected.load();
}

// 获取连接信息
std::string ModbusContextManager::getConnectionInfo() const {
    if (!m_connected.load()) {
        return "未连接";
    }
    
    return m_ip + ":" + std::to_string(m_port) + " (从站ID: " + std::to_string(m_slave_id) + ")";
}

// 检查指针是否由ModbusContextManager管理
bool ModbusContextManager::hasOwnership(void* ptr) {
    std::lock_guard<std::mutex> lock(m_mutex);
    return (m_ctx.get() == ptr);
} 