# GW-Acquirer-Modbus Kubernetes部署配置 (稳定版)
# 网关Modbus数据采集器

---
# ConfigMap - 稳定配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: gw-acquirer-modbus-config
  namespace: default
  labels:
    app: gw-acquirer-modbus
    component: config
    version: v8.0
data:
  # 设备配置 - 3设备稳定运行
  devices.json: |
    {
      "global_defaults": {
        "modbus_port": 502,
        "poll_interval": 2000,
        "func_code": 3,
        "slave_id": 1,
        "start_addr": 0,
        "num_regs": 50,
        "udp_target_ip": "*************",
        "wf_name": "GW_WINDFARM",
        "para1": "XIHE",
        "para2": "WT",
        "para3": "SCADA",
        "para4": "MADEINCHINA",
        "para7": "502",
        "para8": "TOP 28-3.6",
        "auto_discover": false,
        "scan_slaves": false,
        "debug_mode": false,
        "dynamic_adapt": false,
        "max_retries": 2,
        "retry_interval": 3000,
        "quiet_mode": false
      },
      "port_allocation": {
        "udp_base": 6522,
        "http_base": 18085,
        "tcp_base": 65456,
        "nodeport_http_base": 30306,
        "nodeport_tcp_base": 31406
      },
      "devices": [
        {
          "id": "0",
          "modbus_ip": "*************",
          "enabled": true,
          "description": "主控制器设备"
        },
        {
          "id": "1",
          "modbus_ip": "*************",
          "enabled": true,
          "description": "备用控制器设备"
        },
        {
          "id": "2",
          "modbus_ip": "*************",
          "enabled": true,
          "description": "扩展控制器设备"
        }
      ]
    }
  
  # 稳定启动脚本 - 只使用支持的参数
  startup.sh: |
    #!/bin/bash
    set -e
    
    echo "=========================================="
    echo "GW-Acquirer-Modbus 稳定启动脚本 v8.0"
    echo "时间: $(date)"
    echo "Pod: ${HOSTNAME}"
    echo "=========================================="
    
    # 从Pod名称提取设备索引
    DEVICE_INDEX=${HOSTNAME##*-}
    echo "设备索引: $DEVICE_INDEX"
    
    # 验证索引
    if ! [[ "$DEVICE_INDEX" =~ ^[0-9]+$ ]]; then
        echo "❌ 无效的设备索引: $DEVICE_INDEX"
        exit 1
    fi
    
    # 读取配置
    CONFIG_FILE="/app/config/devices.json"
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "❌ 配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    echo "📋 配置验证:"
    if jq empty "$CONFIG_FILE" 2>/dev/null; then
        echo "✅ JSON格式正确"
    else
        echo "❌ JSON格式错误，使用默认配置"
    fi
    
    # 获取设备配置
    DEVICE_CONFIG=$(jq ".devices[$DEVICE_INDEX]" "$CONFIG_FILE" 2>/dev/null)
    if [ "$DEVICE_CONFIG" == "null" ] || [ -z "$DEVICE_CONFIG" ]; then
        echo "❌ 设备配置不存在，索引: $DEVICE_INDEX"
        echo "使用默认配置启动..."
        DEVICE_ID="$DEVICE_INDEX"
        MODBUS_IP="*************"
        DESCRIPTION="默认设备$DEVICE_INDEX"
        ENABLED="true"
    else
        DEVICE_ID=$(echo "$DEVICE_CONFIG" | jq -r '.id' 2>/dev/null)
        MODBUS_IP=$(echo "$DEVICE_CONFIG" | jq -r '.modbus_ip' 2>/dev/null)
        DESCRIPTION=$(echo "$DEVICE_CONFIG" | jq -r '.description' 2>/dev/null)
        ENABLED=$(echo "$DEVICE_CONFIG" | jq -r '.enabled' 2>/dev/null)
    fi
    
    # 检查设备是否启用
    if [ "$ENABLED" != "true" ]; then
        echo "⏭️ 设备已禁用，进入等待状态"
        mkdir -p /tmp/health
        echo "disabled" > /tmp/health/ready
        echo "disabled" > /tmp/health/alive
        while true; do 
            echo "设备禁用中... $(date)"
            sleep 300
        done
    fi
    
    # 获取配置
    DEFAULTS=$(jq '.global_defaults' "$CONFIG_FILE" 2>/dev/null)
    PORT_CONFIG=$(jq '.port_allocation' "$CONFIG_FILE" 2>/dev/null)
    
    # 动态端口分配
    UDP_BASE=$(echo "$PORT_CONFIG" | jq -r '.udp_base' 2>/dev/null || echo "6522")
    HTTP_BASE=$(echo "$PORT_CONFIG" | jq -r '.http_base' 2>/dev/null || echo "18085")
    
    UDP_PORT=$((UDP_BASE + DEVICE_INDEX))
    HTTP_PORT=$((HTTP_BASE + DEVICE_INDEX))
    
    # 获取默认配置
    MODBUS_PORT=$(echo "$DEFAULTS" | jq -r '.modbus_port' 2>/dev/null || echo "502")
    UDP_TARGET_IP=$(echo "$DEFAULTS" | jq -r '.udp_target_ip' 2>/dev/null || echo "*************")
    POLL_INTERVAL=$(echo "$DEFAULTS" | jq -r '.poll_interval' 2>/dev/null || echo "2000")
    WF_NAME=$(echo "$DEFAULTS" | jq -r '.wf_name' 2>/dev/null || echo "GW_WINDFARM")
    
    # UDP包头参数
    PARA1=$(echo "$DEFAULTS" | jq -r '.para1' 2>/dev/null || echo "XIHE")
    PARA2=$(echo "$DEFAULTS" | jq -r '.para2' 2>/dev/null || echo "WT")
    PARA3=$(echo "$DEFAULTS" | jq -r '.para3' 2>/dev/null || echo "SCADA")
    PARA4=$(echo "$DEFAULTS" | jq -r '.para4' 2>/dev/null || echo "MADEINCHINA")
    PARA5=$(printf "%02d" $((DEVICE_INDEX + 1)))
    PARA6="$MODBUS_IP"
    PARA7=$(echo "$DEFAULTS" | jq -r '.para7' 2>/dev/null || echo "502")
    PARA8=$(echo "$DEFAULTS" | jq -r '.para8' 2>/dev/null || echo "TOP 28-3.6")
    
    echo "🚀 稳定启动配置:"
    echo "  设备ID: $DEVICE_ID"
    echo "  设备描述: $DESCRIPTION"
    echo "  Modbus目标: $MODBUS_IP:$MODBUS_PORT"
    echo "  UDP转发: $UDP_TARGET_IP:$UDP_PORT"
    echo "  HTTP端口: $HTTP_PORT"
    echo "  序列号: $PARA5"
    
    # 快速网络测试
    echo "🔍 网络测试..."
    if timeout 2 ping -c 1 "$MODBUS_IP" > /dev/null 2>&1; then
        echo "✅ 网络测试通过: $MODBUS_IP"
    else
        echo "⚠️ 网络测试失败，但继续启动: $MODBUS_IP"
    fi
    
    # 构建启动命令 - 只使用程序支持的参数
    CMD="/app/acquirer-modbus"
    CMD="$CMD --ip_modbus=$MODBUS_IP"
    CMD="$CMD --port_modbus=$MODBUS_PORT"
    CMD="$CMD --ip_udp=$UDP_TARGET_IP"
    CMD="$CMD --port_udp=$UDP_PORT"
    CMD="$CMD --poll_interval=$POLL_INTERVAL"
    CMD="$CMD --func_code=$(echo "$DEFAULTS" | jq -r '.func_code' 2>/dev/null || echo "3")"
    CMD="$CMD --slave_id=$(echo "$DEFAULTS" | jq -r '.slave_id' 2>/dev/null || echo "1")"
    CMD="$CMD --start_addr=$(echo "$DEFAULTS" | jq -r '.start_addr' 2>/dev/null || echo "0")"
    CMD="$CMD --num_regs=$(echo "$DEFAULTS" | jq -r '.num_regs' 2>/dev/null || echo "50")"
    CMD="$CMD --wf_name=${WF_NAME}_${PARA5}"
    CMD="$CMD --port_http=$HTTP_PORT"
    CMD="$CMD --para1=\"$PARA1\""
    CMD="$CMD --para2=\"$PARA2\""
    CMD="$CMD --para3=\"$PARA3\""
    CMD="$CMD --para4=\"$PARA4\""
    CMD="$CMD --para5=\"$PARA5\""
    CMD="$CMD --para6=\"$PARA6\""
    CMD="$CMD --para7=\"$PARA7\""
    CMD="$CMD --para8=\"$PARA8\""
    CMD="$CMD --max_retries=$(echo "$DEFAULTS" | jq -r '.max_retries' 2>/dev/null || echo "2")"
    CMD="$CMD --retry_interval=$(echo "$DEFAULTS" | jq -r '.retry_interval' 2>/dev/null || echo "3000")"
    
    # 创建健康检查文件
    mkdir -p /tmp/health
    echo "ready" > /tmp/health/ready
    echo "alive" > /tmp/health/alive
    
    # 健康检查监控
    (
      while true; do
        if pgrep -f acquirer-modbus > /dev/null; then
          echo "alive" > /tmp/health/alive
          echo "ready" > /tmp/health/ready
        else
          rm -f /tmp/health/alive /tmp/health/ready
        fi
        sleep 30
      done
    ) &
    
    echo "🚀 稳定启动命令: $CMD"
    echo "=========================================="
    
    # 启动采集器
    exec $CMD

---
# StatefulSet - 3设备稳定运行
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: gw-acquirer-modbus
  namespace: default
  labels:
    app: gw-acquirer-modbus
    component: data-collector
    version: v8.0
spec:
  serviceName: gw-acquirer-modbus
  replicas: 3  # 3个设备同时运行
  podManagementPolicy: Parallel  # 并行启动
  selector:
    matchLabels:
      app: gw-acquirer-modbus
  template:
    metadata:
      labels:
        app: gw-acquirer-modbus
        component: data-collector
        version: v8.0
    spec:
      terminationGracePeriodSeconds: 15
      securityContext:
        runAsUser: 0
        runAsGroup: 0
        fsGroup: 0
      containers:
      - name: gw-acquirer-modbus
        image: acquirer-modbus:latest
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash", "/app/config/startup.sh"]
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        ports:
        - containerPort: 18085
          name: http-base
          protocol: TCP
        - containerPort: 65456
          name: tcp-base
          protocol: TCP
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "300m"
        livenessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "test -f /tmp/health/alive"
          initialDelaySeconds: 120
          periodSeconds: 60
          timeoutSeconds: 5
          failureThreshold: 5
        readinessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "test -f /tmp/health/ready"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "test -f /tmp/health/ready"
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 30
      volumes:
      - name: config-volume
        configMap:
          name: gw-acquirer-modbus-config
          defaultMode: 0755
      - name: logs-volume
        emptyDir: {}

---
# Service - Headless服务
apiVersion: v1
kind: Service
metadata:
  name: gw-acquirer-modbus
  namespace: default
  labels:
    app: gw-acquirer-modbus
    component: service
    version: v8.0
spec:
  clusterIP: None
  selector:
    app: gw-acquirer-modbus
  ports:
  - port: 18085
    name: http
    targetPort: 18085
    protocol: TCP
  - port: 65456
    name: tcp-control
    targetPort: 65456
    protocol: TCP

---
# Service - NodePort外部访问 (设备0)
apiVersion: v1
kind: Service
metadata:
  name: gw-acquirer-modbus-nodeport-0
  namespace: default
  labels:
    app: gw-acquirer-modbus
    component: nodeport-service
    device-id: "0"
    version: v8.0
spec:
  type: NodePort
  selector:
    app: gw-acquirer-modbus
    statefulset.kubernetes.io/pod-name: gw-acquirer-modbus-0
  ports:
  - port: 18085
    name: http
    targetPort: 18085
    nodePort: 30306
    protocol: TCP
  - port: 65456
    name: tcp-control
    targetPort: 65456
    nodePort: 31406
    protocol: TCP

---
# Service - NodePort外部访问 (设备1)
apiVersion: v1
kind: Service
metadata:
  name: gw-acquirer-modbus-nodeport-1
  namespace: default
  labels:
    app: gw-acquirer-modbus
    component: nodeport-service
    device-id: "1"
    version: v8.0
spec:
  type: NodePort
  selector:
    app: gw-acquirer-modbus
    statefulset.kubernetes.io/pod-name: gw-acquirer-modbus-1
  ports:
  - port: 18086
    name: http
    targetPort: 18086
    nodePort: 30307
    protocol: TCP
  - port: 65457
    name: tcp-control
    targetPort: 65457
    nodePort: 31407
    protocol: TCP

---
# Service - NodePort外部访问 (设备2)
apiVersion: v1
kind: Service
metadata:
  name: gw-acquirer-modbus-nodeport-2
  namespace: default
  labels:
    app: gw-acquirer-modbus
    component: nodeport-service
    device-id: "2"
    version: v8.0
spec:
  type: NodePort
  selector:
    app: gw-acquirer-modbus
    statefulset.kubernetes.io/pod-name: gw-acquirer-modbus-2
  ports:
  - port: 18087
    name: http
    targetPort: 18087
    nodePort: 30308
    protocol: TCP
  - port: 65458
    name: tcp-control
    targetPort: 65458
    nodePort: 31408
    protocol: TCP
