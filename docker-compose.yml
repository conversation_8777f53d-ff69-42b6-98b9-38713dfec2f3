version: '3.8'

services:
  # Modbus采集器 - 设备1（完整参数配置）
  modbus-collector-1:
    build: .
    container_name: modbus-collector-device-1
    environment:
      # 基本Modbus参数
      - MODBUS_IP=*************
      - MODBUS_PORT=502
      - POLL_INTERVAL=1000
      - FUNC_CODE=3
      - SLAVE_ID=1
      - START_ADDR=0
      - NUM_REGS=2100
      # UDP转发参数
      - IP_UDP=*************
      - PORT_UDP=6522
      # 系统参数
      - WF_NAME=WINDFARM_DEVICE_1
      - PORT_HTTP=18085
      # UDP包头参数（6组核心参数）
      - PARA1=STATION_01
      - PARA2=WIND_TURBINE
      - PARA3=SCADA_SYSTEM
      - PARA4=GOLDWIND
      - PARA5=WT001
      - PARA6=*************
      - PARA7=502
      - PARA8=DEVICE_1_CONTROLLER
      # 高级参数
      - AUTO_DISCOVER=true
      - SCAN_SLAVES=false
      - DEBUG_MODE=false
      - DYNAMIC_ADAPT=true
      - MAX_RETRIES=5
      - RETRY_INTERVAL=10000
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - modbus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/health/alive"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Modbus采集器 - 设备2
  modbus-collector-2:
    build: .
    container_name: modbus-collector-device-2
    environment:
      - MODBUS_IP=*************
      - POLL_INTERVAL=600
      - DEVICE_ID=2
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - modbus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/health/alive"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Modbus采集器 - 设备3
  modbus-collector-3:
    build: .
    container_name: modbus-collector-device-3
    environment:
      - MODBUS_IP=*************
      - POLL_INTERVAL=100
      - DEVICE_ID=3
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - modbus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/health/alive"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  modbus-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  logs:
  config:
