# 保持简单，仅使用必要的变量
CC := g++
TARGET := acquirer-modbus
# 只包含实际存在的源文件
SRC := acquirer-modbus.cpp modbus_context_manager.cpp

# 从SRC生成目标文件列表
OBJ := $(patsubst %.c,%.o,$(patsubst %.cpp,%.o,$(SRC)))

# 严格匹配工作的命令参数
INCLUDES := -I.
LIBDIR := -L.
LIBS := -lmodbus -lco -lpthread -ldl
CFLAGS := -no-pie -std=c++11

# 可移植版本使用静态链接关键库
PORTABLE_FLAGS := -Wl,-Bstatic -lmodbus -lco -Wl,-Bdynamic -lpthread -ldl

# 文件检查列表（保持必要的检查）
REQUIRED_FILES := $(SRC) modbus_context_manager.h ./libmodbus.a ./libco.a

# 默认目标
all: check_deps $(TARGET)
	@echo "Build successful. Output: $(TARGET)"

# 编译目标
$(TARGET): $(OBJ)
	@echo "Linking $@..."
	$(CC) -o $@ $^ $(LIBDIR) $(LIBS) $(CFLAGS)

# 编译C文件的规则
%.o: %.c
	@echo "Compiling $<..."
	$(CC) -c $< -o $@ $(INCLUDES) $(CFLAGS)

# 编译C++文件的规则
%.o: %.cpp
	@echo "Compiling $<..."
	$(CC) -c $< -o $@ $(INCLUDES) $(CFLAGS)

# 可移植版本（适合在不同Linux系统间迁移）
portable: check_deps
	@echo "Building portable version..."
	$(CC) -o $(TARGET)-portable $(SRC) $(INCLUDES) $(LIBDIR) $(PORTABLE_FLAGS) $(CFLAGS)
	@echo "Build successful. Output: $(TARGET)-portable"

# 文件依赖检查
check_deps:
	@echo "Checking dependencies..."
	@for file in $(REQUIRED_FILES); do \
		if [ -e "$$file" ]; then \
			echo "[OK] Found: $$file"; \
		else \
			echo "[ERROR] Missing: $$file"; \
			exit 1; \
		fi; \
	done

# 清理构建产物
clean:
	@echo "Cleaning build artifacts..."
	@rm -f $(TARGET) $(TARGET)-portable $(OBJ)
	@echo "Clean complete"

# 伪目标声明
.PHONY: all portable clean check_deps