#!/bin/bash

# GW-Acquirer-Modbus 本地启动脚本
# 网关Modbus数据采集器 - 本地部署专用
# 版本: v1.0

set -e

# 检测操作系统
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    IS_WINDOWS=true
    EXECUTABLE="./acquirer-modbus.exe"
else
    IS_WINDOWS=false
    EXECUTABLE="./acquirer-modbus"
fi

# 默认配置
DEFAULT_MODBUS_IP="*************"
DEFAULT_UDP_IP="*************"
DEFAULT_UDP_PORT=6522
DEFAULT_POLL_INTERVAL=1000
DEFAULT_LOG_LEVEL="INFO"

# 函数：显示帮助信息
show_help() {
    cat << EOF
GW-Acquirer-Modbus 本地启动脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -m, --modbus-ip IP      Modbus设备IP地址 (默认: $DEFAULT_MODBUS_IP)
    -u, --udp-ip IP         UDP转发目标IP (默认: $DEFAULT_UDP_IP)
    -p, --udp-port PORT     UDP转发端口 (默认: $DEFAULT_UDP_PORT)
    -i, --interval MS       轮询间隔(毫秒) (默认: $DEFAULT_POLL_INTERVAL)
    -d, --daemon            后台运行
    -l, --log-level LEVEL   日志级别 (默认: $DEFAULT_LOG_LEVEL)
    --debug                 启用调试模式
    --auto-discover         启用自动发现
    --stop                  停止运行中的采集器
    --status                查看运行状态

UDP包头参数:
    --para1 VALUE           站点名称 (默认: XIHE)
    --para2 VALUE           系统类型 (默认: WT)
    --para3 VALUE           设备类型 (默认: SCADA)
    --para4 VALUE           制造商 (默认: MADEINCHINA)
    --para5 VALUE           序列号 (默认: 01)
    --para6 VALUE           IP地址 (默认: 同modbus-ip)
    --para7 VALUE           端口号 (默认: 502)
    --para8 VALUE           备注 (默认: TOP 28-3.6)

示例:
    # 基本启动
    $0 -m ************* -u ************* -p 6522

    # 完整参数启动
    $0 -m ************* -u ************* -p 6522 \\
       --para1 "XIHE" --para2 "WT" --para5 "01" --auto-discover

    # 后台运行
    $0 -m ************* -u ************* -p 6522 -d

    # 调试模式
    $0 -m ************* -u ************* -p 6522 --debug

    # Docker模式
    $0 --docker -i *************

    # 后台运行
    $0 -i ************* -d

    # 停止采集器
    $0 --stop
EOF
}

# 函数：检查依赖
check_dependencies() {
    local missing_deps=()
    
    if [ ! -f "$EXECUTABLE" ]; then
        missing_deps+=("acquirer-modbus")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "错误: 缺少依赖: ${missing_deps[*]}"
        echo "请确保程序已正确编译并位于当前目录"
        exit 1
    fi
}

# 函数：创建健康检查
setup_health_check() {
    mkdir -p /tmp/health
    echo "ready" > /tmp/health/ready
    echo "alive" > /tmp/health/alive
    
    # 启动健康检查监控
    (
        while true; do
            if pgrep -f acquirer-modbus > /dev/null; then
                echo "alive" > /tmp/health/alive
                echo "ready" > /tmp/health/ready
            else
                rm -f /tmp/health/alive /tmp/health/ready
            fi
            sleep 5
        done
    ) &
    
    echo "健康检查已启动"
}

# 函数：停止采集器
stop_collector() {
    echo "正在停止Modbus采集器..."
    
    # 查找并终止进程
    if pgrep -f acquirer-modbus > /dev/null; then
        pkill -f acquirer-modbus
        echo "采集器已停止"
    else
        echo "未找到运行中的采集器"
    fi
    
    # 清理健康检查文件
    rm -f /tmp/health/alive /tmp/health/ready
    
    exit 0
}

# 函数：Docker模式启动
start_docker_mode() {
    echo "启动Docker模式..."
    
    # 检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        echo "错误: Docker未安装或不可用"
        exit 1
    fi
    
    # 构建镜像
    echo "构建Docker镜像..."
    docker build -t acquirer-modbus .
    
    # 启动容器
    echo "启动容器..."
    docker run -d \
        --name acquirer-modbus \
        --restart unless-stopped \
        -e MODBUS_IP="$MODBUS_IP" \
        -e POLL_INTERVAL="$POLL_INTERVAL" \
        -v "$(pwd)/logs:/app/logs" \
        -v "$(pwd)/config:/app/config" \
        acquirer-modbus
    
    echo "Docker容器已启动"
    docker ps | grep acquirer-modbus
}

# 函数：Kubernetes模式启动
start_k8s_mode() {
    echo "启动Kubernetes模式..."
    
    # 检查kubectl是否可用
    if ! command -v kubectl &> /dev/null; then
        echo "错误: kubectl未安装或不可用"
        exit 1
    fi
    
    # 应用配置
    echo "应用Kubernetes配置..."
    kubectl apply -f gw-acquirer-modbus.yaml
    
    echo "Kubernetes部署已启动"
    kubectl get pods -l app=acquirer-modbus
}

# 函数：标准模式启动
start_standard_mode() {
    echo "启动标准模式..."
    
    # 检查依赖
    check_dependencies
    
    # 设置健康检查
    setup_health_check
    
    # 构建启动命令
    local cmd="$EXECUTABLE --ip_modbus=$MODBUS_IP --poll_interval=$POLL_INTERVAL"
    
    if [ "$CONFIG_FILE" != "" ]; then
        cmd="$cmd --config=$CONFIG_FILE"
    fi
    
    echo "启动命令: $cmd"
    echo "设备IP: $MODBUS_IP"
    echo "轮询间隔: ${POLL_INTERVAL}ms"
    echo "日志级别: $LOG_LEVEL"
    
    # 启动采集器
    if [ "$DAEMON_MODE" = "true" ]; then
        echo "后台模式启动..."
        mkdir -p logs
        nohup $cmd > logs/collector.log 2>&1 &
        local pid=$!
        echo "采集器已在后台启动，PID: $pid"
        echo "日志文件: logs/collector.log"
        echo "查看日志: tail -f logs/collector.log"
        echo "停止采集器: $0 --stop"
        echo "采集器已在后台启动，PID: $!"
        echo "日志文件: logs/collector.log"
    else
        echo "前台模式启动..."
        exec $cmd
    fi
}

# 解析命令行参数
MODBUS_IP="$DEFAULT_IP"
POLL_INTERVAL="$DEFAULT_POLL_INTERVAL"
LOG_LEVEL="$DEFAULT_LOG_LEVEL"
CONFIG_FILE=""
DAEMON_MODE="false"
DOCKER_MODE="false"
K8S_MODE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -i|--ip)
            MODBUS_IP="$2"
            shift 2
            ;;
        -p|--poll-interval)
            POLL_INTERVAL="$2"
            shift 2
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON_MODE="true"
            shift
            ;;
        -s|--stop)
            stop_collector
            ;;
        --docker)
            DOCKER_MODE="true"
            shift
            ;;
        --k8s)
            K8S_MODE="true"
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 创建必要的目录
mkdir -p logs config

# 根据模式启动
if [ "$DOCKER_MODE" = "true" ]; then
    start_docker_mode
elif [ "$K8S_MODE" = "true" ]; then
    start_k8s_mode
else
    start_standard_mode
fi
