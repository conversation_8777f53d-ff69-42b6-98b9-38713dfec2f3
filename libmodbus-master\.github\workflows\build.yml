name: Build libmodbus

on:
    push:
        branches: ["master"]
    pull_request:
        branches: ["master"]

jobs:
    build:
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v3
            - name: configure
              run: ./autogen.sh && ./configure
            - name: make
              run: make
            - name: make distcheck
              run: make distcheck
