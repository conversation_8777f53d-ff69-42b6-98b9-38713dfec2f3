#!/bin/bash

# Docker容器启动脚本
# 处理环境变量并启动Modbus采集器

set -e

# 设置默认值 - 基本Modbus参数
MODBUS_IP=${MODBUS_IP:-"*************"}
MODBUS_PORT=${MODBUS_PORT:-502}
POLL_INTERVAL=${POLL_INTERVAL:-100}
FUNC_CODE=${FUNC_CODE:-3}
SLAVE_ID=${SLAVE_ID:-1}
START_ADDR=${START_ADDR:-0}
NUM_REGS=${NUM_REGS:-10}

# UDP转发参数
IP_UDP=${IP_UDP:-"127.0.0.1"}
PORT_UDP=${PORT_UDP:-65522}

# 系统参数
WF_NAME=${WF_NAME:-"WF_BEIJING"}
PORT_HTTP=${PORT_HTTP:-18085}

# UDP包头6组参数
PARA1=${PARA1:-"HASEN"}           # 站点名称
PARA2=${PARA2:-"WT"}              # 系统类型
PARA3=${PARA3:-"SCADA"}           # 设备类型
PARA4=${PARA4:-"ZHONGCHE"}        # 制造商名称
PARA5=${PARA5:-"01"}              # 序列号
PARA6=${PARA6:-"***********"}     # IP地址
PARA7=${PARA7:-"2408"}            # 端口号
PARA8=${PARA8:-"TOP 28-3"}        # 备注

# 高级参数
AUTO_DISCOVER=${AUTO_DISCOVER:-false}
SCAN_SLAVES=${SCAN_SLAVES:-false}
RETRY_INTERVAL=${RETRY_INTERVAL:-10000}
MAX_RETRIES=${MAX_RETRIES:-5}
DYNAMIC_ADAPT=${DYNAMIC_ADAPT:-true}
DEBUG_MODE=${DEBUG_MODE:-false}
QUIET_MODE=${QUIET_MODE:-false}

# 显示环境变量（调试用）
echo "=========================================="
echo "环境变量检查:"
echo "基本Modbus参数:"
echo "  MODBUS_IP: '$MODBUS_IP'"
echo "  MODBUS_PORT: '$MODBUS_PORT'"
echo "  POLL_INTERVAL: '$POLL_INTERVAL'"
echo "  FUNC_CODE: '$FUNC_CODE'"
echo "  SLAVE_ID: '$SLAVE_ID'"
echo "  START_ADDR: '$START_ADDR'"
echo "  NUM_REGS: '$NUM_REGS'"
echo "UDP转发参数:"
echo "  IP_UDP: '$IP_UDP'"
echo "  PORT_UDP: '$PORT_UDP'"
echo "UDP包头参数:"
echo "  PARA1(站点名): '$PARA1'"
echo "  PARA2(系统类型): '$PARA2'"
echo "  PARA3(设备类型): '$PARA3'"
echo "  PARA4(制造商): '$PARA4'"
echo "  PARA5(序列号): '$PARA5'"
echo "  PARA6(IP地址): '$PARA6'"
echo "  PARA7(端口): '$PARA7'"
echo "  PARA8(备注): '$PARA8'"
echo "高级参数:"
echo "  AUTO_DISCOVER: '$AUTO_DISCOVER'"
echo "  SCAN_SLAVES: '$SCAN_SLAVES'"
echo "  DEBUG_MODE: '$DEBUG_MODE'"
echo "=========================================="

# 验证关键参数
if [[ ! "$POLL_INTERVAL" =~ ^[0-9]+$ ]]; then
    echo "❌ 错误: POLL_INTERVAL必须是数字，当前值: '$POLL_INTERVAL'"
    exit 1
fi

if [[ ! "$MODBUS_PORT" =~ ^[0-9]+$ ]]; then
    echo "❌ 错误: MODBUS_PORT必须是数字，当前值: '$MODBUS_PORT'"
    exit 1
fi

if [[ ! "$PORT_UDP" =~ ^[0-9]+$ ]]; then
    echo "❌ 错误: PORT_UDP必须是数字，当前值: '$PORT_UDP'"
    exit 1
fi

if [[ ! "$MODBUS_IP" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "❌ 错误: MODBUS_IP格式不正确，当前值: '$MODBUS_IP'"
    echo "   正确格式示例: *************"
    exit 1
fi

if [[ ! "$IP_UDP" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "❌ 错误: IP_UDP格式不正确，当前值: '$IP_UDP'"
    echo "   正确格式示例: *************"
    exit 1
fi

echo "✅ 参数验证通过"

# 创建健康检查文件
mkdir -p /tmp/health
echo "ready" > /tmp/health/ready
echo "alive" > /tmp/health/alive

# 启动健康检查监控
(
    while true; do
        if pgrep -f acquirer-modbus > /dev/null; then
            echo "alive" > /tmp/health/alive
            echo "ready" > /tmp/health/ready
        else
            rm -f /tmp/health/alive /tmp/health/ready
        fi
        sleep 5
    done
) &

# 检查可执行文件
if [ ! -f "./acquirer-modbus" ]; then
    echo "❌ 错误: 找不到可执行文件 ./acquirer-modbus"
    echo "   当前目录: $(pwd)"
    echo "   目录内容:"
    ls -la
    exit 1
fi

if [ ! -x "./acquirer-modbus" ]; then
    echo "❌ 错误: ./acquirer-modbus 没有执行权限"
    echo "   文件权限:"
    ls -la ./acquirer-modbus
    exit 1
fi

echo "✅ 可执行文件检查通过"

# 构建完整的启动命令
CMD="./acquirer-modbus"
CMD="$CMD --ip_modbus=$MODBUS_IP"
CMD="$CMD --port_modbus=$MODBUS_PORT"
CMD="$CMD --ip_udp=$IP_UDP"
CMD="$CMD --port_udp=$PORT_UDP"
CMD="$CMD --poll_interval=$POLL_INTERVAL"
CMD="$CMD --func_code=$FUNC_CODE"
CMD="$CMD --slave_id=$SLAVE_ID"
CMD="$CMD --start_addr=$START_ADDR"
CMD="$CMD --num_regs=$NUM_REGS"
CMD="$CMD --wf_name=$WF_NAME"
CMD="$CMD --port_http=$PORT_HTTP"

# UDP包头参数
CMD="$CMD --para1=$PARA1"
CMD="$CMD --para2=$PARA2"
CMD="$CMD --para3=$PARA3"
CMD="$CMD --para4=$PARA4"
CMD="$CMD --para5=$PARA5"
CMD="$CMD --para6=$PARA6"
CMD="$CMD --para7=$PARA7"
CMD="$CMD --para8=$PARA8"

# 高级参数
if [ "$AUTO_DISCOVER" = "true" ]; then
    CMD="$CMD --auto_discover"
fi

if [ "$SCAN_SLAVES" = "true" ]; then
    CMD="$CMD --scan_slaves"
fi

if [ "$DEBUG_MODE" = "true" ]; then
    CMD="$CMD --debug_mode"
fi

if [ "$QUIET_MODE" = "true" ]; then
    CMD="$CMD --quiet_mode"
fi

if [ "$DYNAMIC_ADAPT" = "true" ]; then
    CMD="$CMD --dynamic_adapt"
fi

CMD="$CMD --retry_interval=$RETRY_INTERVAL"
CMD="$CMD --max_retries=$MAX_RETRIES"

# 显示启动信息
echo "=========================================="
echo "Modbus采集器容器启动"
echo "时间: $(date)"
echo "用户: $(whoami)"
echo "工作目录: $(pwd)"
echo "Modbus目标: $MODBUS_IP:$MODBUS_PORT"
echo "UDP转发: $IP_UDP:$PORT_UDP"
echo "轮询间隔: ${POLL_INTERVAL}ms"
echo "功能码: $FUNC_CODE, 从站ID: $SLAVE_ID"
echo "地址范围: $START_ADDR-$((START_ADDR+NUM_REGS-1)) ($NUM_REGS个寄存器)"
echo "=========================================="

# 启动采集器
echo "完整启动命令:"
echo "$CMD"
echo "=========================================="
exec $CMD
