{"devices": [{"id": 1, "name": "PLC-1", "ip": "*************", "port": 502, "description": "主控PLC设备", "enabled": true, "poll_interval": 100, "timeout": 5000, "retry_count": 3}, {"id": 2, "name": "PLC-2", "ip": "*************", "port": 502, "description": "辅助PLC设备", "enabled": true, "poll_interval": 100, "timeout": 5000, "retry_count": 3}, {"id": 3, "name": "PLC-3", "ip": "*************", "port": 502, "description": "监控PLC设备", "enabled": true, "poll_interval": 100, "timeout": 5000, "retry_count": 3}], "global_settings": {"log_level": "INFO", "max_concurrent_connections": 10, "health_check_interval": 30, "data_retention_days": 7}, "monitoring": {"enabled": true, "metrics_port": 8080, "health_endpoint": "/health", "metrics_endpoint": "/metrics"}}