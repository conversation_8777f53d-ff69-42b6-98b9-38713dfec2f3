# GW-Acquirer-Modbus 部署指南

网关Modbus数据采集器 - 智能批量部署方案 v2.0

## 概述

GW-Acquirer-Modbus是专为工业网关设计的高性能Modbus数据采集器，支持JSON数组驱动的批量设备配置、自动端口分配和智能扩展管理。

## 核心特性 v2.0

- ✅ **JSON数组驱动**: 通过简单的设备数组实现批量配置
- ✅ **自动端口分配**: 基于索引自动计算端口，避免冲突
- ✅ **智能扩展**: 一键添加/删除设备，自动更新副本数
- ✅ **差异化配置**: 只需配置关键差异（IP、ID），其他自动继承
- ✅ **批量管理**: 支持脚本和工具批量操作
- ✅ **完整参数支持**: 24个参数，包含UDP包头6组核心参数
- ✅ **智能自适应**: 自动从站发现和地址范围检测
- ✅ **容器化部署**: Docker和Kubernetes生产级部署

## 快速开始

### 1. 智能批量部署（推荐）

```bash
# 查看当前设备配置
chmod +x manage-devices.sh
./manage-devices.sh list

# 添加新设备（自动分配ID和端口）
./manage-devices.sh add *************
./manage-devices.sh add ************* 06

# 启用/禁用设备
./manage-devices.sh enable 04
./manage-devices.sh disable 03

# 更新副本数并部署
./manage-devices.sh update-replicas
./manage-devices.sh deploy

# 查看部署状态
./manage-devices.sh status
```

### 2. 手动配置部署

#### 修改设备配置
```bash
# 编辑gw-acquirer-modbus.yaml中的devices数组
vim gw-acquirer-modbus.yaml

# 在devices数组中添加新设备
{
  "id": "05",
  "modbus_ip": "*************",
  "enabled": true
}

# 更新副本数（等于enabled设备数量）
# 修改 replicas: 4

# 部署
kubectl apply -f gw-acquirer-modbus.yaml
```

### 3. 传统部署方式

#### 本地部署
```bash
# 基本启动
./start-collector.sh -m ************* -u ************* -p 6522

# 后台运行
./start-collector.sh -m ************* -u ************* -p 6522 -d
```

#### Docker部署
```bash
# 构建镜像
docker build -t acquirer-modbus .

# 基本启动
docker run -d --name gw-acquirer-modbus-01 \
  -e MODBUS_IP=************* \
  -e IP_UDP=************* \
  -e PORT_UDP=6522 \
  -e PARA1="XIHE" \
  -e PARA5="01" \
  acquirer-modbus
```

## 智能配置系统 v2.0

### JSON数组驱动配置
```json
{
  "global_defaults": {
    // 全局默认配置，所有设备继承
    "modbus_port": 502,
    "poll_interval": 1000,
    "func_code": 3,
    "auto_discover": true,
    "para1": "XIHE",
    "para2": "WT",
    "para3": "SCADA"
  },
  "port_allocation": {
    // 自动端口分配基数
    "udp_base": 6522,
    "http_base": 18085,
    "nodeport_http_base": 30001
  },
  "devices": [
    // 只需配置关键差异参数
    {
      "id": "01",
      "modbus_ip": "*************",
      "enabled": true
    },
    {
      "id": "02",
      "modbus_ip": "*************",
      "enabled": true
    }
  ]
}
```

### 自动端口分配策略
```
设备索引 | UDP端口 | HTTP端口 | NodePort HTTP | NodePort TCP
---------|---------|----------|---------------|-------------
0        | 6522    | 18085    | 30001         | 31201
1        | 6523    | 18086    | 30002         | 31202
2        | 6524    | 18087    | 30003         | 31203
3        | 6525    | 18088    | 30004         | 31204
```

### 差异化配置原则
- **必须配置**: `id`（设备标识）, `modbus_ip`（设备IP）
- **可选配置**: `enabled`（启用状态，默认true）
- **自动继承**: 所有其他参数从global_defaults继承
- **自动计算**: 端口基于设备索引自动分配
- **自动生成**: PARA5=设备ID, PARA6=设备IP, 风场名称等

### 完整参数支持 (24个)

#### 基本Modbus参数 (7个)
- `modbus_port`: 502 (默认)
- `poll_interval`: 1000   (默认)
- `func_code`: 3 (默认，支持自适应发现)
- `slave_id`: 1 (默认，支持自适应发现)
- `start_addr`: 0 (默认，支持自适应发现)
- `num_regs`: 100 (默认，支持自适应发现)

#### UDP转发参数 (2个)
- `udp_target_ip`: ************* (默认)
- UDP端口: 自动分配 (6522+索引)

#### UDP包头参数 (8个)
- `para1`: XIHE (站点名称)
- `para2`: WT (系统类型)
- `para3`: SCADA (设备类型)
- `para4`: MADEINCHINA (制造商)
- `para5`: 自动=设备ID (序列号)
- `para6`: 自动=设备IP (IP地址)
- `para7`: 502 (端口号)
- `para8`: TOP 28-3.6 (备注)

#### 高级参数 (7个)
- `auto_discover`: true (自动发现)
- `scan_slaves`: false (扫描从站)
- `debug_mode`: false (调试模式)
- `dynamic_adapt`: true (动态自适应)
- `max_retries`: 5 (最大重试)
- `retry_interval`: 10000ms (重试间隔)
- `quiet_mode`: false (静默模式)

## 批量管理操作指南

### 设备管理命令
```bash
# 查看所有设备
./manage-devices.sh list
# 输出示例:
# 设备01: ************* (启用: true)
# 设备02: ************* (启用: true)
# 设备03: ************* (启用: false)
# 总设备数: 3, 启用设备数: 2

# 添加设备（自动分配ID）
./manage-devices.sh add *************
# ✅ 设备已添加: ID=04, IP=*************

# 添加设备（指定ID）
./manage-devices.sh add ************* 06
# ✅ 设备已添加: ID=06, IP=*************

# 启用/禁用设备
./manage-devices.sh enable 03
./manage-devices.sh disable 04

# 删除设备
./manage-devices.sh remove 06
```

### 批量部署流程
```bash
# 1. 批量添加设备
./manage-devices.sh add *************0
./manage-devices.sh add *************1
./manage-devices.sh add *************2

# 2. 更新副本数（自动计算启用设备数量）
./manage-devices.sh update-replicas
# 📊 启用设备数量: 5
# ✅ 副本数已更新为: 5

# 3. 部署到Kubernetes
./manage-devices.sh deploy
# 🚀 部署到Kubernetes...
# ✅ 部署完成
# ⏳ 等待Pod就绪...

# 4. 查看部署状态
./manage-devices.sh status
```

### 配置工具集成
```bash
# 通过脚本批量添加设备
for ip in 192.168.137.{10..20}; do
    ./manage-devices.sh add $ip
done

# 通过JSON文件批量导入
cat devices.txt | while read ip; do
    ./manage-devices.sh add $ip
done

# 批量启用特定范围设备
for id in {05..10}; do
    ./manage-devices.sh enable $(printf "%02d" $id)
done
```

## Kubernetes部署架构 v2.0

### 智能部署架构
```
gw-acquirer-modbus.yaml
├── ConfigMap (JSON设备配置 + 启动脚本)
├── StatefulSet (单一StatefulSet，多Pod)
│   ├── Pod-0 (设备01: *************)
│   ├── Pod-1 (设备02: *************)
│   └── Pod-N (设备N+1: 自动配置)
├── Service (Headless服务发现)
└── NodePort Service (外部访问)
```

### 自动端口分配
```yaml
Pod索引 | 设备ID | Modbus IP      | UDP端口 | HTTP端口 | NodePort
--------|--------|----------------|---------|----------|----------
0       | 01     | *************  | 6522    | 18085    | 30001
1       | 02     | *************  | 6523    | 18086    | 30002
2       | 03     | *************  | 6524    | 18087    | 30003
N       | N+1    | 配置的IP        | 6522+N  | 18085+N  | 30001+N
```

### 扩展设备（零配置）
```bash
# 方法1: 使用管理脚本（推荐）
./manage-devices.sh add *************5
./manage-devices.sh update-replicas
./manage-devices.sh deploy

# 方法2: 直接编辑JSON配置
# 在gw-acquirer-modbus.yaml的devices数组中添加:
{
  "id": "15",
  "modbus_ip": "*************5",
  "enabled": true
}
# 然后更新replicas数量并重新部署
```

## 监控和维护

### 健康检查
```bash
# 本地检查
test -f /tmp/health/alive && echo "运行正常" || echo "运行异常"

# Docker检查
docker exec gw-acquirer-modbus-01 test -f /tmp/health/alive

# Kubernetes检查
kubectl exec gw-acquirer-modbus-01-0 -- test -f /tmp/health/alive
```

### 日志查看
```bash
# 本地日志
tail -f logs/gw-acquirer-modbus.log

# Docker日志
docker logs -f gw-acquirer-modbus-01

# Kubernetes日志
kubectl logs -f gw-acquirer-modbus-01-0
```

### 性能监控
```bash
# Docker资源监控
docker stats gw-acquirer-modbus-01

# Kubernetes资源监控
kubectl top pods -l app=gw-acquirer-modbus
```

## 故障排除

### 常见问题

1. **UDP参数未生效**
   ```bash
   # 检查环境变量
   docker exec gw-acquirer-modbus-01 env | grep -E "(IP_UDP|PORT_UDP)"
   
   # 检查启动日志
   docker logs gw-acquirer-modbus-01 | grep "UDP转发"
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   kubectl get services -o wide | grep NodePort
   
   # 修改端口配置
   kubectl edit service gw-acquirer-modbus-01
   ```

3. **连接失败**
   ```bash
   # 测试网络连通性
   kubectl exec gw-acquirer-modbus-01-0 -- ping *************
   kubectl exec gw-acquirer-modbus-01-0 -- telnet ************* 502
   ```

## 最佳实践

### 生产环境建议
1. **使用Kubernetes部署**: 提供高可用和自动恢复
2. **配置资源限制**: 防止资源耗尽
3. **启用健康检查**: 确保服务稳定性
4. **集中日志管理**: 便于问题排查
5. **监控告警**: 及时发现异常

### 安全建议
1. **网络隔离**: 使用专用网络或VPN
2. **访问控制**: 限制NodePort访问范围
3. **定期更新**: 保持镜像和依赖最新
4. **备份配置**: 定期备份重要配置

### 性能优化
1. **调整轮询间隔**: 根据实际需求优化
2. **资源配置**: 合理分配CPU和内存
3. **网络优化**: 确保低延迟网络连接
4. **批量处理**: 启用自动发现减少连接数

## 技术支持

如遇问题，请提供：
- 部署方式和环境信息
- 完整的错误日志
- 网络配置和设备信息
- 参数配置详情

## v2.0 核心优势

### 🚀 批量管理优势
- **一键扩展**: 添加设备只需一条命令
- **自动端口分配**: 无需手动计算端口冲突
- **零配置继承**: 新设备自动继承全局配置
- **智能副本管理**: 自动计算和更新Pod副本数

### 🎯 配置简化优势
- **差异化配置**: 只需配置IP和ID，其他自动处理
- **JSON数组驱动**: 通过简单数组实现复杂部署
- **工具友好**: 支持脚本和自动化工具集成
- **版本控制**: JSON配置便于版本管理和回滚

### 🔧 运维便利性
- **统一管理**: 单一YAML文件管理所有设备
- **状态可视**: 清晰的设备状态和端口分配
- **快速部署**: 从配置到部署一键完成
- **灵活扩展**: 支持从1个到100+个设备扩展

### 📊 生产就绪特性
- **自动发现**: 智能Modbus参数自适应
- **健康监控**: 完善的Pod健康检查
- **资源管理**: 合理的CPU和内存限制
- **故障恢复**: StatefulSet自动重启和恢复

---

**版本**: v2.0 (智能批量部署版本)
**更新时间**: 2025-06-18
**产品**: GW-Acquirer-Modbus 网关Modbus数据采集器
**核心特性**: JSON数组驱动 + 自动端口分配 + 批量管理
