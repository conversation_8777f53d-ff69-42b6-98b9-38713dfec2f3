# 多阶段构建Dockerfile for Modbus采集器
FROM ubuntu:22.04 AS builder

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    g++ \
    make \
    jq \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制所有源代码和依赖
COPY . .

# 编译程序
RUN make clean && make

# 运行时镜像
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    jq \
    bash \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 从构建阶段复制编译好的程序
COPY --from=builder /app/acquirer-modbus .
COPY --from=builder /app/lib*.a .

# 设置执行权限
RUN chmod +x acquirer-modbus

# 创建健康检查目录
RUN mkdir -p /tmp/health

# 复制启动脚本并设置权限（在切换用户前）
COPY docker-entrypoint.sh /app/
RUN chmod +x /app/docker-entrypoint.sh

# 创建非root用户并设置权限
RUN useradd -r -s /bin/false modbus && \
    chown -R modbus:modbus /app /tmp/health

# 切换到非root用户
USER modbus

# 暴露Modbus端口
EXPOSE 502

# 配置环境变量，使容器支持参数化
ENV MODBUS_IP="*************" \
    POLL_INTERVAL=100

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD test -f /tmp/health/alive || exit 1

# 容器启动命令
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD []