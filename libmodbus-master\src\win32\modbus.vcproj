<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9,00"
	Name="modbus"
	ProjectGUID="{498E0845-C7F4-438B-8EDE-EF7FC9A74430}"
	RootNamespace="modbus"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			EnableManagedIncrementalBuild="0"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
				Description=""
				CommandLine=""
				AdditionalDependencies=""
				Outputs=""
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				WholeProgramOptimization="false"
				AdditionalIncludeDirectories="..\src;..;.;D:/include/msvc_std"
				PreprocessorDefinitions="W32DEBUG;HAVE_CONFIG_H;DLLBUILD;_CRT_SECURE_NO_DEPRECATE=1;_CRT_NONSTDC_NO_DEPRECATE=1"
				MinimalRebuild="false"
				ExceptionHandling="0"
				BasicRuntimeChecks="2"
				RuntimeLibrary="1"
				FloatingPointModel="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
				CompileAs="1"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_MSC_VER"
				ResourceOutputFileName="$(SolutionDir)/modbus.res"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="ws2_32.lib"
				Version="1.0"
				LinkIncremental="1"
				AdditionalLibraryDirectories=""
				GenerateManifest="true"
				GenerateDebugInformation="true"
				GenerateMapFile="true"
				SubSystem="1"
				RandomizedBaseAddress="0"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			WholeProgramOptimization="1"
			EnableManagedIncrementalBuild="0"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
				Description=""
				CommandLine=""
				AdditionalDependencies=""
				Outputs=""
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				EnableIntrinsicFunctions="true"
				WholeProgramOptimization="false"
				AdditionalIncludeDirectories="..\src;..;.;D:/include/msvc_std"
				PreprocessorDefinitions="HAVE_CONFIG_H;DLLBUILD;_CRT_SECURE_NO_DEPRECATE=1;_CRT_NONSTDC_NO_DEPRECATE=1"
				ExceptionHandling="0"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="false"
				FloatingPointModel="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="0"
				CompileAs="1"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies="ws2_32.lib"
				LinkIncremental="0"
				GenerateManifest="true"
				GenerateDebugInformation="true"
				SubSystem="1"
				StackReserveSize="1048576"
				StackCommitSize="524288"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				LinkTimeCodeGeneration="0"
				EntryPointSymbol=""
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
				SuppressStartupBanner="false"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(SolutionDir)$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
				Description=""
				CommandLine=""
				AdditionalDependencies=""
				Outputs=""
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				WholeProgramOptimization="false"
				AdditionalIncludeDirectories="$(SolutionDir)"
				PreprocessorDefinitions=""
				MinimalRebuild="false"
				BasicRuntimeChecks="3"
				RuntimeLibrary="0"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
				CompileAs="1"
				DisableSpecificWarnings="4244;4267"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="1"
				StackReserveSize="1048576"
				StackCommitSize="524288"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(SolutionDir)$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
				Description=""
				CommandLine=""
				AdditionalDependencies=""
				Outputs=""
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				EnableIntrinsicFunctions="true"
				WholeProgramOptimization="false"
				AdditionalIncludeDirectories="$(SolutionDir)"
				PreprocessorDefinitions=""
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
				CompileAs="1"
				DisableSpecificWarnings="4244;4267"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="1"
				GenerateDebugInformation="true"
				SubSystem="1"
				StackReserveSize="1048576"
				StackCommitSize="524288"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				LinkTimeCodeGeneration="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="..\modbus-data.c"
				>
			</File>
			<File
				RelativePath="..\modbus-rtu.c"
				>
			</File>
			<File
				RelativePath="..\modbus-tcp.c"
				>
			</File>
			<File
				RelativePath="..\modbus.c"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath="config.h"
				>
			</File>
			<File
				RelativePath="..\modbus-private.h"
				>
			</File>
			<File
				RelativePath="..\modbus-rtu-private.h"
				>
			</File>
			<File
				RelativePath="..\modbus-rtu.h"
				>
			</File>
			<File
				RelativePath="..\modbus-tcp-private.h"
				>
			</File>
			<File
				RelativePath="..\modbus-tcp.h"
				>
			</File>
			<File
				RelativePath="modbus-version.h"
				>
			</File>
			<File
				RelativePath="..\modbus.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
			<File
				RelativePath=".\modbus.rc"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
