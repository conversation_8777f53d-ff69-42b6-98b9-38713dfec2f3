# GW-Acquirer-Modbus 项目 .gitignore 文件
# 网关Modbus数据采集器项目专用

# ================================
# 编译产物和可执行文件
# ================================
# 编译生成的目标文件
*.o
*.obj
*.exe

# 主程序可执行文件
acquirer-modbus
acquirer-modbus-portable
acquirer-modbus.exe

# ================================
# 超大文件和镜像文件
# ================================
# tar镜像文件（通常很大，不适合版本控制）
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz

# Docker镜像导出文件
*.docker

# ================================
# 日志文件和运行时文件
# ================================
# 日志目录
logs/
*.log
*.log.*

# 运行时临时文件
/tmp/
*.pid
*.lock

# 健康检查文件
/tmp/health/

# ================================
# 备份文件和临时文件
# ================================
# 备份目录
bak/
backup/
*.bak
*.backup
*~

# 临时文件
*.tmp
*.temp
*.swp
*.swo

# ================================
# IDE和编辑器文件
# ================================
# Visual Studio Code
.vscode/
*.code-workspace

# CLion
.idea/
cmake-build-*/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ================================
# 系统文件
# ================================
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# ================================
# 构建和依赖管理
# ================================
# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
!makefile

# Conan
conandata.yml
conanfile.txt
conaninfo.txt

# ================================
# 容器和部署相关
# ================================
# Docker volumes
docker-volumes/

# Kubernetes secrets
secrets/
*.secret

# ================================
# 开发和测试文件
# ================================
# 测试输出
test-results/
coverage/
*.gcov
*.gcda
*.gcno

# 性能分析
*.prof
gmon.out

# ================================
# 配置文件（敏感信息）
# ================================
# 包含敏感信息的配置文件
*.env
.env.*
config/local.*
config/production.*

# SSL证书和密钥
*.key
*.pem
*.crt
*.p12

# ================================
# 项目特定排除
# ================================
# 特定的大文件或不需要版本控制的文件
# （根据实际情况调整）

# 编译时生成的依赖文件
*.d
*.dep

# 调试信息文件
*.dSYM/
*.pdb

# ================================
# 保留重要文件的说明
# ================================
# 以下文件需要保留在版本控制中：
# - libmodbus.a (静态库)
# - libco.a (协程库)
# - co/ (头文件目录)
# - modbus/ (头文件目录)
# - libmodbus-master/ (源码目录)
# - config/devices.json (配置模板)
# - *.cpp, *.h (源代码)
# - makefile (构建配置)
# - Dockerfile (容器构建)
# - *.sh (脚本文件)
# - *.yml, *.yaml (配置文件)
# - *.md (文档文件)
