#ifndef MODBUS_CONTEXT_MANAGER_H
#define MODBUS_CONTEXT_MANAGER_H

#include <mutex>
#include <memory>
#include <string>
#include <atomic>
#include <condition_variable>
#include <functional>
#include <chrono>
#include <thread>
#include "modbus/modbus.h"
#include <set>

// 全局Modbus上下文管理器类，用于安全地管理Modbus连接
class ModbusContextManager {
private:
    std::shared_ptr<modbus_t> m_ctx;  // 使用智能指针管理ctx
    std::mutex m_mutex;                // 保护上下文操作的互斥锁
    std::string m_ip;                  // 当前连接的IP
    int m_port;                        // 当前连接的端口
    int m_slave_id;                    // 当前从站ID
    std::atomic<bool> m_connected;     // 连接状态
    std::atomic<bool> m_reconnecting;  // 重连状态标志
    std::condition_variable m_cond;    // 条件变量用于等待连接状态变化
    std::atomic<uint64_t> m_ctx_version; // 上下文版本号，用于检测更新
    std::atomic<int64_t> m_last_error_time; // 最后一次错误发生时间
    std::atomic<int64_t> m_last_success_time; // 最后一次成功操作时间
    std::atomic<int> m_error_count;     // 错误计数器
    std::atomic<int> m_consecutive_errors; // 连续错误计数
    std::atomic<uint64_t> m_total_operations; // 总操作计数
    std::atomic<uint64_t> m_successful_operations; // 成功操作计数
    
    // 增强错误跟踪
    std::atomic<int> m_last_error_code;  // 最后一次错误代码
    std::string m_last_error_op;         // 最后一次失败的操作名称
    std::atomic<int> m_eagain_errors;    // EAGAIN错误计数器 
    std::atomic<int> m_illegal_addr_errors; // 非法地址错误计数器
    std::atomic<int> m_timeout_errors;   // 超时错误计数器
    std::atomic<int> m_connection_attempts; // 连接尝试次数
    std::atomic<bool> m_force_recovery;  // 强制恢复标志
    std::atomic<int64_t> m_last_reset_time; // 最后一次重置时间
    
    // 引用计数相关成员
    std::atomic<int> m_reference_count;  // 当前上下文的引用计数
    std::mutex m_ref_mutex;              // 保护引用计数操作的互斥锁
    std::condition_variable m_ref_cond;  // 条件变量用于等待引用计数变为0
    
    // 两阶段锁定状态
    std::atomic<bool> m_context_locked;  // 当设置为true时，不允许新的引用获取
    std::atomic<bool> m_reconnect_pending; // 当设置为true时，表示有待处理的重连请求
    std::atomic<bool> m_releasing_context; // 当设置为true时，表示正在释放上下文
    
    // 自定义删除器，确保安全释放Modbus上下文
    struct ModbusFreeDeleter {
        void operator()(modbus_t* ctx) {
            if (ctx) {
                try {
                    static std::mutex delete_mutex;
                    std::lock_guard<std::mutex> lock(delete_mutex);
                    
                    // 在释放前检查指针有效性
                    if (!ctx) {
                        printf("ModbusContextManager: 指针为NULL，跳过释放\n");
                        return;
                    }
                    
                    // 使用静态集合记录已经释放的ctx地址，防止double free
                    static std::set<uintptr_t> freed_contexts;
                    uintptr_t ctx_addr = reinterpret_cast<uintptr_t>(ctx);
                    
                    // 检查是否已经释放过
                    if (freed_contexts.find(ctx_addr) != freed_contexts.end()) {
                        printf("ModbusContextManager: 检测到潜在的double free，跳过释放\n");
                        return;
                    }
                    
                    // 执行实际释放操作
                    printf("ModbusContextManager: 准备释放Modbus上下文 %p\n", ctx);
                    modbus_close(ctx);
                    modbus_free(ctx);
                    
                    // 记录释放的上下文地址
                    freed_contexts.insert(ctx_addr);
                    
                    // 清理过多的记录以避免内存增长
                    if (freed_contexts.size() > 100) {
                        // 只保留后50个记录
                        auto it = freed_contexts.begin();
                        std::advance(it, freed_contexts.size() - 50);
                        freed_contexts.erase(freed_contexts.begin(), it);
                    }
                    
                    printf("ModbusContextManager: Modbus上下文已安全释放\n");
                } catch (const std::exception& e) {
                    printf("ModbusContextManager: 释放Modbus上下文时发生异常: %s\n", e.what());
                    try {
                        // 即使关闭失败，也尝试释放资源
                        modbus_free(ctx);
                    } catch (...) {
                        printf("ModbusContextManager: 无法释放Modbus上下文，可能发生内存泄漏\n");
                    }
                } catch (...) {
                    printf("ModbusContextManager: 释放Modbus上下文时发生未知异常\n");
                    try {
                        modbus_free(ctx);
                    } catch (...) {
                        // 忽略异常
                    }
                }
            }
        }
    };
    
public:
    ModbusContextManager();
    
    ~ModbusContextManager();
    
    // 创建TCP连接
    bool createTcpConnection(const std::string& ip, int port, int slave_id);
    
    // 获取当前上下文（线程安全）
    std::shared_ptr<modbus_t> getContext();
    
    // 获取上下文版本号
    uint64_t getContextVersion() const {
        return m_ctx_version.load();
    }
    
    // 检查是否已连接
    bool isConnected() const;
    
    // 获取连接信息
    std::string getConnectionInfo() const;
    
    // 安全获取上下文引用 - 新方法
    std::shared_ptr<modbus_t> acquireContextReference(const char* operation_name = nullptr);
    
    // 安全释放上下文引用 - 新方法
    void releaseContextReference(const char* operation_name = nullptr);
    
    // 等待所有引用释放 - 新方法
    bool waitForReferenceCountZero(int timeout_ms = 5000);
    
    // 获取当前引用计数 - 新方法
    int getReferenceCount() const {
        return m_reference_count.load();
    }
    
    // 记录错误 - 增强版
    void recordError(int error_code = 0, const char* operation_name = nullptr) {
        auto current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()
        ).count();
        
        m_error_count.fetch_add(1);
        m_consecutive_errors.fetch_add(1);
        m_total_operations.fetch_add(1);
        m_last_error_time.store(current_time);
        m_last_error_code.store(error_code);
        
        if (operation_name) {
            m_last_error_op = operation_name;
        }
        
        // 根据错误类型更新特定计数器
        if (error_code == EAGAIN) {
            // 资源临时不可用错误
            m_eagain_errors.fetch_add(1);
            printf("ModbusContextManager: 检测到EAGAIN错误 (累计: %d), 操作='%s'\n", 
                   m_eagain_errors.load(), operation_name ? operation_name : "unknown");
                   
            // 如果EAGAIN错误过多，考虑重置连接
            if (m_eagain_errors.load() > 5 && !m_force_recovery.load()) {
                printf("ModbusContextManager: EAGAIN错误累积过多，设置强制恢复标志\n");
                m_force_recovery.store(true);
            }
        } 
        else if (error_code == 112345680 || error_code == EMBXILADD) {
            // 非法地址错误
            m_illegal_addr_errors.fetch_add(1);
            printf("ModbusContextManager: 检测到非法地址错误 (累计: %d), 操作='%s'\n", 
                   m_illegal_addr_errors.load(), operation_name ? operation_name : "unknown");
        }
        else if (error_code == ETIMEDOUT || error_code == EMBXGTAR) {
            // 超时错误
            m_timeout_errors.fetch_add(1);
            printf("ModbusContextManager: 检测到超时错误 (累计: %d), 操作='%s'\n", 
                   m_timeout_errors.load(), operation_name ? operation_name : "unknown");
                   
            // 如果超时错误过多，考虑重置连接
            if (m_timeout_errors.load() > 3 && !m_force_recovery.load()) {
                printf("ModbusContextManager: 超时错误累积过多，设置强制恢复标志\n");
                m_force_recovery.store(true);
            }
        }
        
        // 记录错误详细信息
        if (operation_name) {
            printf("ModbusContextManager: 操作 '%s' 失败, 错误码=%d (%s)\n", 
                   operation_name, error_code, modbus_strerror(error_code));
        }
        
        // 检查是否需要强制恢复
        if (m_force_recovery.load() && m_connected.load() && !m_reconnecting.load()) {
            printf("ModbusContextManager: 检测到强制恢复标志，准备重置连接\n");
            return; // 由调用者处理重连逻辑
        }
        
        // 检查连续错误是否过多，可能需要重连
        if (m_consecutive_errors.load() >= 3 && m_connected.load() && !m_reconnecting.load()) {
            printf("ModbusContextManager: 检测到连续 %d 次错误，可能需要重新连接\n", 
                   m_consecutive_errors.load());
        }
    }
    
    // 记录成功操作
    void recordSuccess(const char* operation_name = nullptr) {
        auto current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()
        ).count();
        
        m_consecutive_errors.store(0); // 重置连续错误计数
        m_successful_operations.fetch_add(1);
        m_total_operations.fetch_add(1);
        m_last_success_time.store(current_time);
        
        // 如果之前设置了强制恢复标志，现在成功了可以重置
        if (m_force_recovery.load()) {
            m_force_recovery.store(false);
            printf("ModbusContextManager: 操作成功，重置强制恢复标志\n");
        }
        
        // 每100次成功操作重置部分错误计数器
        if (m_successful_operations.load() % 100 == 0) {
            m_eagain_errors.store(0);
            m_timeout_errors.store(0);
            printf("ModbusContextManager: 已成功执行 %llu 次操作，重置错误计数器\n", 
                   (unsigned long long)m_successful_operations.load());
        }
        
        // 记录详细调试信息，但降低日志频率
        if (operation_name && m_successful_operations.load() % 100 == 0) {
            printf("ModbusContextManager: 已成功执行 %llu 次操作\n", 
                   (unsigned long long)m_successful_operations.load());
        }
    }
    
    // 重置错误计数器
    void resetErrorCount() {
        m_error_count.store(0);
        m_consecutive_errors.store(0);
    }
    
    // 获取操作成功率
    double getSuccessRate() const {
        uint64_t total = m_total_operations.load();
        if (total == 0) return 1.0; // 避免除以零
        
        return static_cast<double>(m_successful_operations.load()) / total;
    }
    
    // 安全地创建新连接 - 增强版
    bool createConnection(const char* ip, int port, int slave_id = 1) {
        // 记录连接尝试
        m_connection_attempts.fetch_add(1);
        auto current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()
        ).count();
        m_last_reset_time.store(current_time);
        
        // 防止多线程同时尝试重连
        if (m_reconnecting.exchange(true)) {
            printf("ModbusContextManager: 另一个线程正在创建连接，等待... (尝试次数: %d)\n", 
                   m_connection_attempts.load());
            
            // 等待重连完成，使用更长的等待时间
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
            
            // 如果已连接，则返回成功
            if (m_connected.load()) {
                printf("ModbusContextManager: 另一个线程已成功创建连接\n");
                return true;
            }
            
            // 如果等待时间过长，强制重置重连标志
            auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()
            ).count();
            
            if (now - current_time > 2000) { // 如果等待超过2秒
                printf("ModbusContextManager: 等待连接超时，强制重置重连标志\n");
                m_reconnecting.store(false); // 强制重置
            }
            
            // 尝试获取锁并重新连接
        }
        
        // 使用try_lock进一步避免死锁
        std::unique_lock<std::mutex> lock(m_mutex, std::try_to_lock);
        if (!lock.owns_lock()) {
            printf("ModbusContextManager: 无法获取锁，等待...\n");
            // 等待一段时间后再次尝试
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            lock = std::unique_lock<std::mutex>(m_mutex, std::try_to_lock);
            
            if (!lock.owns_lock()) {
                printf("ModbusContextManager: 仍然无法获取锁，可能存在死锁，强制重置\n");
                m_reconnecting.store(false);
                return false;
            }
        }
        
        printf("ModbusContextManager: 创建新连接 %s:%d (从站ID=%d, 尝试次数: %d)\n", 
               ip, port, slave_id, m_connection_attempts.load());
        
        // 先关闭并释放旧的连接
        if (m_ctx) {
            // 先拿到原始指针，然后将智能指针置空，防止在错误处理过程中多次释放
            modbus_t* old_ctx = m_ctx.get();
            m_ctx.reset(); // 先重置智能指针，防止出现野指针
            
            if (old_ctx) {
                // 避免释放已关闭的连接导致段错误
                try {
                    printf("ModbusContextManager: 关闭现有连接\n");
                    modbus_close(old_ctx);
                    // 不在这里释放，留给ModbusFreeDeleter处理
                } catch (const std::exception& e) {
                    printf("ModbusContextManager: 关闭连接时发生异常: %s\n", e.what());
                } catch (...) {
                    // 忽略关闭时的错误
                    printf("ModbusContextManager: 关闭连接时发生未知异常\n");
                }
                
                // 使用单独的try-catch块处理释放，确保即使关闭失败也能释放资源
                try {
                    printf("ModbusContextManager: 释放旧的Modbus上下文\n");
                    modbus_free(old_ctx);
                } catch (const std::exception& e) {
                    printf("ModbusContextManager: 释放资源时发生异常: %s\n", e.what());
                } catch (...) {
                    printf("ModbusContextManager: 释放资源时发生未知异常\n");
                }
                
                // 安全延迟，确保资源完全释放
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        
        // 标记连接已断开
        m_connected.store(false);
        
        // 等待一段时间再创建新连接，避免过快重连导致的资源争用
        // 根据连接尝试次数增加等待时间，实现指数退避
        int backoff_ms = std::min(100 * (1 << std::min(m_connection_attempts.load(), 5)), 2000);
        printf("ModbusContextManager: 等待 %d ms 后尝试创建新连接 (退避策略)\n", backoff_ms);
        std::this_thread::sleep_for(std::chrono::milliseconds(backoff_ms));
        
        // 尝试创建新的连接
        printf("ModbusContextManager: 尝试创建新的Modbus上下文\n");
        modbus_t* new_ctx = nullptr;
        
        try {
            // 重置相关错误计数器
            m_eagain_errors.store(0);
            m_timeout_errors.store(0);
            m_force_recovery.store(false);
            
            new_ctx = modbus_new_tcp(ip, port);
            if (!new_ctx) {
                int err = errno; // 保存错误码
                printf("ModbusContextManager: 创建上下文失败: %s (错误码: %d)\n", 
                       modbus_strerror(err), err);
                recordError(err, "createConnection-new_tcp");
                m_reconnecting.store(false);
                return false;
            }
            
            // 设置从站地址
            if (modbus_set_slave(new_ctx, slave_id) == -1) {
                int err = errno; // 保存错误码
                printf("ModbusContextManager: 设置从站ID失败: %s (错误码: %d)\n", 
                       modbus_strerror(err), err);
                modbus_free(new_ctx);
                recordError(err, "createConnection-set_slave");
                m_reconnecting.store(false);
                return false;
            }
            
            // 设置超时和错误恢复模式
            struct timeval timeout;
            timeout.tv_sec = 2;   // 增加超时时间，提高稳定性
            timeout.tv_usec = 0;
            if (modbus_set_response_timeout(new_ctx, timeout.tv_sec, timeout.tv_usec) == -1) {
                int err = errno; // 保存错误码
                printf("ModbusContextManager: 设置响应超时失败: %s (错误码: %d)\n", 
                       modbus_strerror(err), err);
                // 不终止连接，只记录警告
            }
            
            // 设置调试模式为FALSE
            modbus_set_debug(new_ctx, FALSE);
            
            // 设置错误恢复模式
            modbus_set_error_recovery(new_ctx, 
                (modbus_error_recovery_mode)(MODBUS_ERROR_RECOVERY_LINK | MODBUS_ERROR_RECOVERY_PROTOCOL));
            
            // 添加连接前的额外延迟，防止过快连接导致服务器拒绝
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            // 尝试连接
            printf("ModbusContextManager: 尝试连接到Modbus服务器 %s:%d...\n", ip, port);
            if (modbus_connect(new_ctx) == -1) {
                int err = errno; // 保存错误码
                printf("ModbusContextManager: 连接失败: %s (错误码: %d)\n", 
                       modbus_strerror(err), err);
                modbus_free(new_ctx);
                recordError(err, "createConnection-connect");
                m_reconnecting.store(false);
                return false;
            }
            
            // 连接成功，更新状态
            printf("ModbusContextManager: 连接成功！IP=%s, 端口=%d, 从站ID=%d\n", ip, port, slave_id);
            m_ctx = std::shared_ptr<modbus_t>(new_ctx, ModbusFreeDeleter());
            m_ip = ip;
            m_port = port;
            m_slave_id = slave_id;
            m_connected.store(true);
            m_ctx_version++;  // 增加版本号
            resetErrorCount(); // 重置错误计数
            recordSuccess("createConnection"); // 记录成功
            
            // 通知等待的线程连接状态已变更
            m_cond.notify_all();
            m_reconnecting.store(false);
            
            // 连接成功后发送一个测试请求，验证连接可用性
            uint8_t test_buffer[1];
            if (modbus_read_bits(new_ctx, 0, 1, test_buffer) == -1) {
                int err = errno;
                printf("ModbusContextManager: 连接测试失败: %s (错误码: %d)\n", 
                       modbus_strerror(err), err);
                // 不因测试请求失败而断开连接，只记录警告
                recordError(err, "createConnection-test");
            } else {
                printf("ModbusContextManager: 连接测试成功\n");
            }
            
            return true;
        } catch (const std::exception& e) {
            printf("ModbusContextManager: 创建连接时发生异常: %s\n", e.what());
            if (new_ctx) {
                try {
                    modbus_free(new_ctx);
                } catch (...) {
                    // 忽略异常
                }
            }
            recordError(errno, "createConnection-exception");
            m_reconnecting.store(false);
            return false;
        } catch (...) {
            printf("ModbusContextManager: 创建连接时发生未知异常\n");
            if (new_ctx) {
                try {
                    modbus_free(new_ctx);
                } catch (...) {
                    // 忽略异常
                }
            }
            recordError(errno, "createConnection-unknown");
            m_reconnecting.store(false);
            return false;
        }
    }
    
    // 等待连接建立
    bool waitForConnection(int timeout_ms) {
        if (m_connected.load()) {
            return true;  // 已经连接
        }
        
        std::unique_lock<std::mutex> lock(m_mutex);
        auto pred = [this]() { return m_connected.load(); };
        if (timeout_ms > 0) {
            return m_cond.wait_for(lock, std::chrono::milliseconds(timeout_ms), pred);
        } else {
            m_cond.wait(lock, pred);
            return true;
        }
    }
    
    // 安全地关闭连接
    void closeConnection();
    
    // 检测并重置连接（如果连接已断开或出错次数过多）
    bool checkAndResetConnection() {
        // 检查是否需要重置连接
        bool need_reset = false;
        
        // 检查是否已经有重连操作正在进行
        if (m_context_locked.load() && (m_reconnect_pending.load() || m_releasing_context.load())) {
            printf("ModbusContextManager: 已有重连操作正在进行，跳过本次检查\n");
            return m_connected.load();
        }
        
        // 情况1: 连接已断开
        if (!m_connected.load()) {
            printf("ModbusContextManager: 检测到连接已断开，准备重新连接\n");
            need_reset = true;
        }
        // 情况2: 错误次数过多
        else if (m_error_count.load() > 5) {
            printf("ModbusContextManager: 错误次数过多 (%d)，准备重置连接\n", 
                   m_error_count.load());
            need_reset = true;
        }
        // 情况3: 强制恢复标志已设置
        else if (m_force_recovery.load()) {
            printf("ModbusContextManager: 检测到强制恢复标志，准备重置连接\n");
            need_reset = true;
        }
        // 情况4: EAGAIN错误过多
        else if (m_eagain_errors.load() > 5) {
            printf("ModbusContextManager: EAGAIN错误过多 (%d)，准备重置连接\n", 
                   m_eagain_errors.load());
            need_reset = true;
        }
        // 情况5: 超时错误过多
        else if (m_timeout_errors.load() > 3) {
            printf("ModbusContextManager: 超时错误过多 (%d)，准备重置连接\n", 
                   m_timeout_errors.load());
            need_reset = true;
        }
        // 情况6: 检测到文件描述符无效
        else if (m_last_error_code.load() == EBADF) {
            printf("ModbusContextManager: 检测到文件描述符无效错误，准备重置连接\n");
            need_reset = true;
        }
        
        if (need_reset) {
            printf("ModbusContextManager: 看门狗检测到需要重连\n");
            
            // 记录重置时间
            auto current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()
            ).count();
            
            // 避免频繁重置
            if (m_last_reset_time.load() > 0) {
                int64_t elapsed_ms = current_time - m_last_reset_time.load();
                if (elapsed_ms < 5000) { // 至少间隔5秒
                    printf("ModbusContextManager: 上次重置时间过近 (%lld ms)，延迟重置\n", 
                           (long long)elapsed_ms);
                    // 仍然重置错误计数器，但不重置连接
                    m_error_count.store(0);
                    m_eagain_errors.store(0);
                    m_timeout_errors.store(0);
                    m_force_recovery.store(false);
                    return m_connected.load();
                }
            }
            
            // 更新重置时间
            m_last_reset_time.store(current_time);
            
            // 首先锁定上下文，阻止新的引用获取
            m_context_locked.store(true);
            m_reconnect_pending.store(true);
            
            printf("ModbusContextManager: 看门狗已锁定上下文，准备重连\n");
            
            // 等待所有现有引用释放（最多等待3秒）
            if (m_reference_count.load() > 0) {
                printf("ModbusContextManager: 看门狗等待现有引用释放 (当前引用数: %d)...\n", 
                       m_reference_count.load());
                if (!waitForReferenceCountZero(3000)) {
                    printf("ModbusContextManager: 警告：等待引用释放超时，将强制重连\n");
                }
            }
            
            // 先安全关闭连接
            closeConnection();
            
            // 等待一段时间再重新连接，避免过快重连导致的资源争用
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            
            // 尝试重新连接
            printf("ModbusContextManager: 看门狗尝试重新连接到 %s:%d\n", m_ip.c_str(), m_port);
            bool result = createConnection(m_ip.c_str(), m_port);
            
            // 重置状态标志 - createConnection会设置m_context_locked为false
            // 但如果连接失败，我们需要确保重置这些标志
            if (!result) {
                m_reconnect_pending.store(false);
                m_context_locked.store(false); // 重置锁定状态，允许后续操作继续
                printf("ModbusContextManager: 看门狗重连失败，已解锁上下文\n");
            }
            
            return result;
        }
        
        return m_connected.load();
    }
    
    // 执行安全的读取操作
    template<typename Func>
    int safeRead(int func_code, int addr, int count, void* buffer, Func readFunc) {
        // 尝试获取上下文引用（而不是直接使用m_ctx）
        std::shared_ptr<modbus_t> ctx = acquireContextReference("safeRead");
        
        // 检查引用是否获取成功
        if (!ctx) {
            printf("ModbusContextManager: 无法获取上下文引用，操作无法执行\n");
            return -1;  // 未连接或上下文已锁定
        }
        
        // 使用RAII模式确保引用总是被释放
        struct ReferenceReleaser {
            ModbusContextManager* manager;
            ReferenceReleaser(ModbusContextManager* m) : manager(m) {}
            ~ReferenceReleaser() {
                manager->releaseContextReference("safeRead");
            }
        } releaser(this);
        
        // 执行读取操作
        try {
            int result = readFunc(ctx.get(), addr, count, buffer);
            if (result == -1) {
                int err = errno;
                // 特殊处理"Bad file descriptor"错误
                if (err == EBADF) {
                    printf("ModbusContextManager: 检测到文件描述符无效错误，连接可能已被关闭\n");
                    m_connected.store(false);  // 标记连接状态为断开
                    if (!m_context_locked.load() && !m_reconnect_pending.load() && !m_releasing_context.load()) {
                        printf("ModbusContextManager: 自动触发重连机制\n");
                    }
                }
                // 记录错误
                recordError(err, "safeRead");
            } else {
                // 记录成功
                recordSuccess("safeRead");
            }
            return result;
        } catch (const std::exception& e) {
            printf("ModbusContextManager: 执行读取操作时发生异常: %s\n", e.what());
            recordError(errno, "safeRead-exception");
            return -1;
        } catch (...) {
            printf("ModbusContextManager: 执行读取操作时发生未知异常\n");
            recordError(errno, "safeRead-unknown");
            return -1;
        }
    }
    
    // 读取bit类型数据的便捷方法
    int readBits(int addr, int count, uint8_t* buffer) {
        return safeRead(1, addr, count, buffer, 
            [](modbus_t* ctx, int a, int c, void* b) { 
                return modbus_read_bits(ctx, a, c, (uint8_t*)b); 
            });
    }
    
    // 读取input bit类型数据的便捷方法
    int readInputBits(int addr, int count, uint8_t* buffer) {
        return safeRead(2, addr, count, buffer, 
            [](modbus_t* ctx, int a, int c, void* b) { 
                return modbus_read_input_bits(ctx, a, c, (uint8_t*)b); 
            });
    }
    
    // 读取register类型数据的便捷方法
    int readRegisters(int addr, int count, uint16_t* buffer) {
        return safeRead(3, addr, count, buffer, 
            [](modbus_t* ctx, int a, int c, void* b) { 
                return modbus_read_registers(ctx, a, c, (uint16_t*)b); 
            });
    }
    
    // 读取input register类型数据的便捷方法
    int readInputRegisters(int addr, int count, uint16_t* buffer) {
        return safeRead(4, addr, count, buffer, 
            [](modbus_t* ctx, int a, int c, void* b) { 
                return modbus_read_input_registers(ctx, a, c, (uint16_t*)b); 
            });
    }
    
    // 获取当前IP
    std::string getIP() const {
        return m_ip;
    }
    
    // 获取当前端口
    int getPort() const {
        return m_port;
    }
    
    // 设置从站ID
    bool setSlaveID(int slave_id) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_ctx || !m_connected.load()) {
            return false;
        }
        
        try {
            bool result = (modbus_set_slave(m_ctx.get(), slave_id) != -1);
            if (!result) {
                recordError();
            }
            return result;
        } catch (...) {
            recordError();
            return false;
        }
    }
    
    // 设置响应超时
    bool setResponseTimeout(uint32_t sec, uint32_t usec) {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_ctx || !m_connected.load()) {
            return false;
        }
        
        try {
            bool result = (modbus_set_response_timeout(m_ctx.get(), sec, usec) != -1);
            if (!result) {
                recordError();
            }
            return result;
        } catch (...) {
            recordError();
            return false;
        }
    }

    // 检查指针是否由ModbusContextManager管理
    bool hasOwnership(void* ptr);
};

// 全局实例
extern ModbusContextManager g_modbus_manager;

#endif // MODBUS_CONTEXT_MANAGER_H 